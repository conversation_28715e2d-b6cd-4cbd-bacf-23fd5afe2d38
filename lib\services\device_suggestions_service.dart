import 'package:workshop_studio/services/database_service.dart';

class DeviceSuggestionsService {
  final MySQLService _dbService;

  DeviceSuggestionsService(this._dbService);

  /// Fetch all machine types for suggestions
  Future<List<String>> getMachineTypes() async {
    try {
      final result = await _dbService.query("SELECT m_type FROM machine_type");
      final List<String> types = [];
      for (final row in result.rows) {
        final type = row.colByName('m_type');
        if (type != null && type.isNotEmpty) {
          types.add(type);
        }
      }
      // Remove duplicates and sort
      final uniqueTypes = types.toSet().toList();
      uniqueTypes.sort();
      return uniqueTypes;
    } catch (e) {
      return [];
    }
  }

  /// Fetch all machine brands for suggestions
  Future<List<String>> getMachineBrands() async {
    try {
      final result = await _dbService.query(
        "SELECT m_brand FROM machine_brand",
      );
      final List<String> brands = [];
      for (final row in result.rows) {
        final brand = row.colByName('m_brand');
        if (brand != null && brand.isNotEmpty) {
          brands.add(brand);
        }
      }
      // Remove duplicates and sort
      final uniqueBrands = brands.toSet().toList();
      uniqueBrands.sort();
      return uniqueBrands;
    } catch (e) {
      return [];
    }
  }

  /// Fetch all machine models for suggestions
  Future<List<String>> getMachineModels() async {
    try {
      final result = await _dbService.query(
        "SELECT m_model FROM machine_model",
      );
      final List<String> models = [];
      for (final row in result.rows) {
        final model = row.colByName('m_model');
        if (model != null && model.isNotEmpty) {
          models.add(model);
        }
      }
      // Remove duplicates and sort
      final uniqueModels = models.toSet().toList();
      uniqueModels.sort();
      return uniqueModels;
    } catch (e) {
      return [];
    }
  }

  /// Fetch all machine series for suggestions
  Future<List<String>> getMachineSeries() async {
    try {
      final result = await _dbService.query(
        "SELECT m_serie FROM machine_serie",
      );
      final List<String> series = [];
      for (final row in result.rows) {
        final serie = row.colByName('m_serie');
        if (serie != null && serie.isNotEmpty) {
          series.add(serie);
        }
      }
      // Remove duplicates and sort
      final uniqueSeries = series.toSet().toList();
      uniqueSeries.sort();
      return uniqueSeries;
    } catch (e) {
      return [];
    }
  }

  /// Fetch all machine problems for suggestions
  Future<List<String>> getMachineProblems() async {
    try {
      final result = await _dbService.query(
        "SELECT m_problem FROM machine_problem",
      );
      final List<String> problems = [];
      for (final row in result.rows) {
        final problem = row.colByName('m_problem');
        if (problem != null && problem.isNotEmpty) {
          problems.add(problem);
        }
      }
      // Remove duplicates and sort
      final uniqueProblems = problems.toSet().toList();
      uniqueProblems.sort();
      return uniqueProblems;
    } catch (e) {
      return [];
    }
  }

  /// Fetch all suggestions at once for better performance
  Future<Map<String, List<String>>> getAllSuggestions() async {
    try {
      final results = await Future.wait([
        getMachineTypes(),
        getMachineBrands(),
        getMachineModels(),
        getMachineSeries(),
        getMachineProblems(),
      ]);

      return {
        'types': results[0],
        'brands': results[1],
        'models': results[2],
        'series': results[3],
        'problems': results[4],
      };
    } catch (e) {
      return {
        'types': <String>[],
        'brands': <String>[],
        'models': <String>[],
        'series': <String>[],
        'problems': <String>[],
      };
    }
  }
}
